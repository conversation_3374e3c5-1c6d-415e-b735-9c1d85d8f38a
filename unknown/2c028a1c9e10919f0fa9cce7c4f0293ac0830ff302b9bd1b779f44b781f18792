// components/customNavBar/customNavBar.js
Component({
  properties: {
    // 页面标题
    title: {
      type: String,
      value: ''
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      value: '#4B8BF5'
    },
    // 文字颜色
    textColor: {
      type: String,
      value: '#ffffff'
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    // 是否显示分享按钮
    showShare: {
      type: Boolean,
      value: true
    },
    // 是否显示反馈按钮
    showFeedback: {
      type: Boolean,
      value: true
    }
  },

  data: {
    statusBarHeight: 0,
    titleBarHeight: 44,
    navBarHeight: 0
  },

  lifetimes: {
    attached() {
      this.setNavBarInfo();
    }
  },

  methods: {
    // 设置导航栏信息
    setNavBarInfo() {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      const titleBarHeight = 44; // 标题栏高度，一般为44px

      this.setData({
        statusBarHeight: statusBarHeight,
        titleBarHeight: titleBarHeight,
        navBarHeight: statusBarHeight + titleBarHeight
      });
    },

    // 返回按钮点击
    onBackTap() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        // 如果没有上一页，跳转到首页
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    },

    // 分享按钮点击
    onShareTap() {
      // 触发页面的分享功能
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 如果页面有自定义分享方法，则调用
      if (currentPage && typeof currentPage.onCustomShare === 'function') {
        currentPage.onCustomShare();
      } else {
        // 默认分享行为
        this.triggerDefaultShare();
      }
    },

    // 反馈按钮点击
    onFeedbackTap() {
      wx.navigateTo({
        url: '/pages/feedback/feedback'
      });
    },

    // 触发默认分享
    triggerDefaultShare() {
      // 获取当前页面信息
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage ? currentPage.route : '';

      // 构建分享信息
      const shareInfo = {
        title: this.data.title || '个人简历模板Offer必来',
        path: '/' + route,
        imageUrl: '/pages/index/images/share-logo.png' // 需要准备分享图片
      };

      // 显示分享菜单
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });

      // 模拟分享操作
      wx.showToast({
        title: '点击右上角分享',
        icon: 'none',
        duration: 2000
      });


    }
  }
});
