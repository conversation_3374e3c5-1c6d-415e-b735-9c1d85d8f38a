<!-- 简历样式选择页面 -->
<view class="resume-style-page">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="header-content">
      <view class="subtitle-container">
        <view class="icon">✨</view>
        <view class="subtitle-text">
          <text class="highlight">选择您喜欢的简历样式</text>
          <text class="normal">点击模板开始制作简历</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 加载状态 -->
    <view wx:if="{{isLoading && templates.length === 0}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载模板...</text>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{errorMessage && templates.length === 0}}" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-message">{{errorMessage}}</text>
      <button class="retry-btn" bindtap="onRetry">重新加载</button>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{!isLoading && templates.length === 0}}" class="empty-container">
      <view class="empty-icon">📄</view>
      <text class="empty-message">暂无可用模板</text>
      <button class="retry-btn" bindtap="onRetry">刷新</button>
    </view>

    <!-- 模板列表 -->
    <view wx:else class="template-grid">
      <view 
        wx:for="{{templates}}" 
        wx:key="id"
        class="template-item {{selectedTemplateId === item.id ? 'selected' : ''}}"
        bindtap="onTemplateSelect"
        data-template="{{item}}"
        data-index="{{index}}"
      >
        <!-- 模板缩略图 -->
        <view class="template-image-container">
          <!-- 加载状态 -->
          <view wx:if="{{item.isLoading}}" class="image-loading">
            <view class="loading-spinner small"></view>
          </view>
          
          <!-- 图片 -->
          <image 
            class="template-image"
            src="{{item.thumbnail}}"
            mode="aspectFill"
            bindload="onImageLoad"
            binderror="onImageError"
            data-index="{{index}}"
            lazy-load="{{true}}"
          />
          
          <!-- 选中状态覆盖层 -->
          <view wx:if="{{selectedTemplateId === item.id}}" class="selected-overlay">
            <view class="selected-icon">✓</view>
            <!-- 填写简历信息按钮 -->
            <button class="create-resume-btn-overlay" bindtap="onCreateResume" catchtap="onCreateResume">
              <text class="btn-text">填写简历信息</text>
            </button>
          </view>

          <!-- 付费标识 -->
          <view wx:if="{{item.is_premium}}" class="premium-badge">
            <text>PRO</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多状态 -->
    <view wx:if="{{isLoadingMore}}" class="loading-more">
      <view class="loading-spinner small"></view>
      <text class="loading-text">加载更多...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view wx:elif="{{!hasMore && templates.length > 0}}" class="no-more">
      <text>已显示全部样式</text>
    </view>
  </view>

  <!-- 底部操作栏已移除，按钮现在显示在选中的缩略图上 -->
</view>
