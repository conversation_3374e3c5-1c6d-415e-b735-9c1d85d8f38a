/* 简历样式选择页面样式 */
.resume-style-page {
  width: 100%;
  min-height: 100vh;
  background: #4B8BF5;
  display: flex;
  flex-direction: column;
}

/* 顶部标题栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #4B8BF5;
  padding: 10rpx 30rpx 20rpx 30rpx;
  color: white;
  z-index: 1000;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.subtitle-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.subtitle-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.highlight {
  color: #4B8BF5;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.normal {
  color: #666;
  font-size: 26rpx;
}

/* 主要内容区域 */
.content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 0rpx 0rpx 0 0;
  margin-top: 160rpx; /* 为固定header留出空间 */
  padding: 30rpx 20rpx;
  min-height: calc(100vh - 200rpx);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.retry-btn {
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-message {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

/* 模板网格 */
.template-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0 10rpx;
}

/* 模板项 */
.template-item {
  background: white;
  border-radius: 0rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 3rpx solid transparent;
}

.template-item.selected {
  border-color: #4B8BF5;
  box-shadow: 0 6rpx 20rpx rgba(75, 139, 245, 0.3);
  transform: translateY(-2rpx);
}

/* 模板图片容器 */
.template-image-container {
  position: relative;
  width: 100%;
  /* A4纸比例 1:1.414，这里设置高度为宽度的1.414倍 */
  aspect-ratio: 1 / 1.414;
  background: #f5f5f5;
  overflow: hidden;
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  z-index: 1;
}

/* 选中状态覆盖层 */
.selected-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(75, 139, 245, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.selected-icon {
  width: 60rpx;
  height: 60rpx;
  background: #4B8BF5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 覆盖层上的按钮 */
.create-resume-btn-overlay {
  background: linear-gradient(135deg, #4B8BF5, #667eea);
  color: white;
  border: none;
  border-radius: 0rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(75, 139, 245, 0.4);
  min-width: 200rpx;
  text-align: center;
}

.create-resume-btn-overlay .btn-text {
  font-size: 26rpx;
  color: white;
}

/* 付费标识 */
.premium-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  z-index: 3;
}

/* 模板信息部分已移除，不再显示标题和标签 */

/* 评分相关样式已移除，因为新接口不提供评分数据 */

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-more .loading-text {
  margin-left: 20rpx;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 底部操作栏样式已移除，按钮现在显示在选中的缩略图覆盖层上 */
