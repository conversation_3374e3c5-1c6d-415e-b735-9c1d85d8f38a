// 简历样式选择页面
const resumeStyleApi = require('../../utils/api/resumeStyleApi');

Page({
  data: {
    templates: [],              // 模板列表
    selectedTemplateId: '',     // 选中的模板ID
    selectedTemplate: null,     // 选中的模板对象
    isLoading: false,           // 是否正在加载
    isRefreshing: false,        // 是否正在刷新
    isLoadingMore: false,       // 是否正在加载更多
    hasMore: true,              // 是否还有更多数据
    skip: 0,                    // 跳过的记录数
    limit: 20,                  // 每次加载数量

    // 筛选和排序参数
    currentCategory: '',        // 当前分类
    currentSort: 'popular'      // 当前排序方式
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('=== 简历样式页面加载 ===');
    console.log('页面参数:', options);
    
    // 如果有传入的分类参数，设置当前分类
    if (options.category) {
      this.setData({
        currentCategory: options.category
      });
    }
    
    this.loadTemplates();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 页面显示时刷新数据（如果列表为空）
    if (this.data.templates.length === 0) {
      this.loadTemplates();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadTemplates(true);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    console.log('上拉加载更多');
    this.loadMoreTemplates();
  },

  /**
   * 加载模板列表
   * @param {boolean} isRefresh - 是否为刷新操作
   */
  async loadTemplates(isRefresh = false) {
    if (this.data.isLoading && !isRefresh) return;

    try {
      this.setData({
        isLoading: !isRefresh,
        isRefreshing: isRefresh
      });

      const skip = isRefresh ? 0 : this.data.skip;
      const params = {
        skip: skip,
        limit: this.data.limit,
        sort: this.data.currentSort
      };

      // 如果有分类筛选，添加分类参数
      if (this.data.currentCategory) {
        params.category = this.data.currentCategory;
      }

      console.log('加载模板参数:', params);

      const response = await resumeStyleApi.getResumeStyleList(params);
      console.log('模板列表响应:', response);

      if (response && response.templates) {
        const newTemplates = response.templates || [];
        
        // 处理模板数据
        const processedTemplates = newTemplates.map((template, index) => ({
          ...template,
          // 添加UI状态字段，与免费模板页面保持一致
          isLoading: true,  // 初始化时设置为加载状态
          imageError: false, // 初始化错误状态
          // 确保有唯一标识符
          uniqueKey: `${template.id}_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`
        }));

        let finalTemplates;
        if (isRefresh) {
          finalTemplates = processedTemplates;
        } else {
          // 去重处理：基于id字段去重
          const existingIds = new Set(this.data.templates.map(t => t.id));
          const uniqueNewTemplates = processedTemplates.filter(template => !existingIds.has(template.id));
          finalTemplates = [...this.data.templates, ...uniqueNewTemplates];

          // 如果过滤掉了重复数据，记录日志
          if (uniqueNewTemplates.length < processedTemplates.length) {
            console.warn(`过滤掉 ${processedTemplates.length - uniqueNewTemplates.length} 个重复模板`);
          }
        }

        const total = response.total || 0;
        const currentTotal = finalTemplates.length;
        const hasMore = response.has_more !== undefined ? response.has_more : (currentTotal < total);

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: isRefresh ? processedTemplates.length : this.data.skip + (finalTemplates.length - this.data.templates.length),
          isLoading: false,
          isRefreshing: false
        });

        // 停止下拉刷新
        if (isRefresh) {
          wx.stopPullDownRefresh();
          if (processedTemplates.length > 0) {
            wx.showToast({
              title: '刷新成功',
              icon: 'success',
              duration: 1500
            });
          }
        }

        console.log('模板加载完成:', {
          新增数量: isRefresh ? processedTemplates.length : (finalTemplates.length - this.data.templates.length),
          总数量: finalTemplates.length,
          还有更多: hasMore
        });

      } else {
        throw new Error('获取模板列表失败');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);

      let errorMessage = '网络连接失败，请检查网络';
      if (error.message) {
        if (error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请联系管理员';
        }
      }

      this.setData({
        isLoading: false,
        isRefreshing: false
      });

      // 停止下拉刷新
      if (isRefresh) {
        wx.stopPullDownRefresh();
      }

      // 简化错误处理，只使用微信官方Toast提示
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 加载更多模板
   */
  async loadMoreTemplates() {
    if (this.data.isLoadingMore || !this.data.hasMore) return;

    try {
      this.setData({
        isLoadingMore: true
      });

      const params = {
        skip: this.data.skip,
        limit: this.data.limit,
        sort: this.data.currentSort
      };

      if (this.data.currentCategory) {
        params.category = this.data.currentCategory;
      }

      const response = await resumeStyleApi.getResumeStyleList(params);

      if (response && response.templates) {
        const newTemplates = response.templates || [];
        
        // 处理模板数据
        const processedTemplates = newTemplates.map((template, index) => ({
          ...template,
          // 添加UI状态字段，与免费模板页面保持一致
          isLoading: true,  // 初始化时设置为加载状态
          imageError: false, // 初始化错误状态
          uniqueKey: `${template.id}_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`
        }));

        // 去重处理
        const existingIds = new Set(this.data.templates.map(t => t.id));
        const uniqueNewTemplates = processedTemplates.filter(template => !existingIds.has(template.id));

        if (uniqueNewTemplates.length < processedTemplates.length) {
          console.warn(`加载更多时过滤掉 ${processedTemplates.length - uniqueNewTemplates.length} 个重复模板`);
        }

        const finalTemplates = [...this.data.templates, ...uniqueNewTemplates];
        const total = response.total || 0;
        const hasMore = response.has_more !== undefined ? response.has_more : (finalTemplates.length < total);

        this.setData({
          templates: finalTemplates,
          hasMore: hasMore,
          skip: this.data.skip + uniqueNewTemplates.length,
          isLoadingMore: false
        });

        console.log('加载更多完成:', {
          新增数量: uniqueNewTemplates.length,
          总数量: finalTemplates.length,
          还有更多: hasMore
        });
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({
        isLoadingMore: false
      });

      // 简化错误处理，只使用微信官方Toast提示
      wx.showToast({
        title: '加载更多失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 重试加载
   */
  onRetry() {
    console.log('重试加载');
    this.setData({
      templates: [],
      skip: 0,
      hasMore: true,
      selectedTemplateId: '',
      selectedTemplate: null
    });
    this.loadTemplates();
  },

  /**
   * 模板选择
   */
  onTemplateSelect(e) {
    const template = e.currentTarget.dataset.template;
    const index = e.currentTarget.dataset.index;
    
    console.log('选择模板:', template);
    console.log('模板索引:', index);

    if (!template || !template.id) {
      wx.showToast({
        title: '模板信息无效',
        icon: 'none'
      });
      return;
    }

    // 更新选中状态
    this.setData({
      selectedTemplateId: template.id,
      selectedTemplate: template
    });

    console.log('模板选中成功:', {
      id: template.id,
      name: template.name
    });

    // 触觉反馈
    // wx.vibrateShort({
    //   type: 'light'
    // });
  },

  /**
   * 创建简历 - 跳转到简历制作页面
   */
  onCreateResume() {
    if (!this.data.selectedTemplate) {
      wx.showToast({
        title: '请先选择模板',
        icon: 'none'
      });
      return;
    }

    console.log('开始创建简历，选中模板:', this.data.selectedTemplate);

    // 跳转到简历制作页面，传递选中的模板信息
    wx.navigateTo({
      url: `/pages/makeResume/makeResume?templateId=${this.data.selectedTemplate.id}&templateName=${encodeURIComponent(this.data.selectedTemplate.name)}`
    });
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      templates[index].isLoading = false;
      this.setData({
        templates: templates
      });
    }
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    const templates = this.data.templates;
    if (templates[index]) {
      const template = templates[index];
      console.warn(`模板缩略图加载失败: ${template.id}, 原始URL: ${template.thumbnail}`);

      template.isLoading = false;
      template.imageError = true;

      // 使用base64编码的占位符图片
      template.thumbnail = 'data:image/svg+xml;base64,' + btoa(`
        <svg width="280" height="400" xmlns="http://www.w3.org/2000/svg">
          <rect width="280" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          <g transform="translate(140, 200)">
            <rect x="-40" y="-50" width="80" height="100" fill="#6c757d" rx="5"/>
            <polygon points="30,-50 30,-30 50,-30" fill="#495057"/>
            <line x1="-30" y1="-30" x2="20" y2="-30" stroke="white" stroke-width="3"/>
            <line x1="-30" y1="-15" x2="25" y2="-15" stroke="white" stroke-width="3"/>
            <line x1="-30" y1="0" x2="25" y2="0" stroke="white" stroke-width="3"/>
            <line x1="-30" y1="15" x2="20" y2="15" stroke="white" stroke-width="3"/>
            <line x1="-30" y1="30" x2="15" y2="30" stroke="white" stroke-width="3"/>
          </g>
          <text x="140" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#6c757d">简历模板</text>
          <text x="140" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#adb5bd">暂无预览</text>
        </svg>
      `);

      this.setData({
        templates: templates
      });
    }
  },
});

//   /**
//    * 页面分享
//    */
//   onShareAppMessage() {
//     const shareData = {
//       title: '精美简历模板 - 选择您喜欢的样式',
//       path: '/pages/resumeStyle/resumeStyle',
//       imageUrl: '/images/share-resume-style.png'
//     };

//     // 如果有选中的模板，使用模板信息
//     if (this.data.selectedTemplate) {
//       shareData.title = `${this.data.selectedTemplate.name} - 精美简历模板`;
//       shareData.path = `/pages/resumeStyle/resumeStyle?templateId=${this.data.selectedTemplate.id}`;
//       if (this.data.selectedTemplate.thumbnail) {
//         shareData.imageUrl = this.data.selectedTemplate.thumbnail;
//       }
//     }

//     console.log('分享数据:', shareData);
//     return shareData;
//   },

//   /**
//    * 分享到朋友圈
//    */
//   onShareTimeline() {
//     return {
//       title: '精美简历模板 - 快速制作专业简历',
//       imageUrl: '/images/share-resume-style.png'
//     };
//   }
// });
