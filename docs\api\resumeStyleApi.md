# 简历样式模板API接口文档

## 1. 获取简历样式模板列表

### 接口描述
获取可用的简历样式模板列表，用于在简历样式页面展示给用户选择。

### 请求信息
- **接口地址**: `/free-templates/styles`
- **请求方法**: `GET`
- **请求头**:
  ```
  Content-Type: application/json
  Authorization: Bearer {userToken} (可选，用于个性化推荐)
  ```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| skip | number | 否 | 跳过的记录数，用于分页 | 0 |
| limit | number | 否 | 每页返回的记录数，默认20，最大50 | 20 |
| category | string | 否 | 模板分类筛选 | "business", "creative", "simple" |
| sort | string | 否 | 排序方式 | "popular", "newest", "rating" |

### 请求示例
```
GET /free-templates/styles?skip=0&limit=20&category=business&sort=popular
```

### 响应数据格式

#### 成功响应 (200)
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "templates": [
      {
        "id": "bgdy/表格单页01.jpg",
        "thumb_url": "http://localhost:18081/static/free_resume_templates/bgdy/表格单页01.jpg"
      },
      {
        "id": "bgdy/表格单页02.jpg",
        "thumb_url": "http://localhost:18081/static/free_resume_templates/bgdy/表格单页02.jpg"
      }
    ],
    "total": 45,
    "skip": 0,
    "limit": 20,
    "has_more": true
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

### 字段说明

#### 模板对象字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 模板唯一标识符，格式如"bgdy/表格单页01.jpg" |
| thumb_url | string | 缩略图URL地址 |

#### 分类映射说明
- `business`: 对应数据库中的 `bgdy` 批次（表格单页类型）
- `creative`: 对应数据库中的 `d100` 批次（千鸟集优化简历）
- `simple`: 对应数据库中的 `jydy` 批次（简约单页类型）

#### 响应元数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | number | 总记录数 |
| skip | number | 跳过的记录数 |
| limit | number | 每页记录数 |
| has_more | boolean | 是否还有更多数据 |

### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 注意事项
1. 缩略图URL需要支持HTTPS协议
2. 图片建议尺寸为280x400像素，保持2:3的宽高比
3. 接口支持跨域访问
4. 建议客户端实现图片懒加载和缓存机制
5. 模板ID在系统中必须唯一且不可变更

## 2. 获取模板详情 (预留接口)

### 接口描述
获取指定模板的详细信息，包括更多预览图、使用说明等。

### 请求信息
- **接口地址**: `/resume/styles/{id}`
- **请求方法**: `GET`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 模板ID |

### 响应数据格式
```json
{
  "code": 200,
  "message": "获取成功", 
  "data": {
    "id": "style_001",
    "name": "商务经典模板",
    "description": "适合商务、金融等正式场合的专业简历模板",
    "thumbnail": "https://example.com/thumbnails/style_001.jpg",
    "preview_images": [
      "https://example.com/previews/style_001_1.jpg",
      "https://example.com/previews/style_001_2.jpg"
    ],
    "category": "business",
    "tags": ["商务", "正式", "经典"],
    "popularity": 95,
    "rating": 4.8,
    "download_count": 1250,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:45:00Z",
    "is_premium": false,
    "preview_url": "https://example.com/previews/style_001.html",
    "usage_guide": "这是一个经典的商务风格简历模板，适合金融、咨询、管理等正式行业使用..."
  }
}
```
