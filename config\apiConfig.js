// 服务器 URL 配置
const apiConfig = {
    // 全局超时配置
    timeout: {
        // 简历预览图片生成超时时间（毫秒）
        previewImage: 10000, // 10秒
        // PDF生成超时时间（毫秒）
        generatePDF: 10000,  // 10秒
        // 普通API请求超时时间（毫秒）
        default: 5000        // 5秒
    },
    dev: {
        // 基础URL
        // baseUrl: 'http://192.168.1.218:18080',
        baseUrl: 'http://127.0.0.1:18080',

        // 简历相关API
        renderTemplateUrl:        '/resume/renderTemplateA01', // 测试环境
        generatePDFUrl:           '/resume/export-pdf',
        exportJpegUrl:            '/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl:                 '/auth/login', // 用户登录
        userInfoUrl:              '/auth/user', // 获取用户信息
        updateUserInfoUrl:        '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl:              '/feedback/submit', // 提交用户反馈

        // 模板相关API
        templatesUrl:             '/templates',        // 获取模板列表
        templateDetailUrl:        '/templates',        // 获取模板详情
        templateDownloadUrl:      '/templates',        // 记录模板下载
        templateCategoriesUrl:    '/templates/categories', // 获取模板分类
        templateSearchUrl:        '/templates/search', // 搜索模板
        popularTemplatesUrl:      '/templates/popular', // 获取热门模板

        // 简历样式相关API
        resumeStylesUrl:          '/free-templates/styles',    // 获取简历样式模板列表
        resumeStyleDetailUrl:     '/free-templates/styles',    // 获取简历样式模板详情
    },
    test: {
        // 基础URL
        // baseUrl: 'http://192.168.1.218:18080',
        baseUrl: 'http://127.0.0.1:18080',

        // 简历相关API
        renderTemplateUrl: 'http://192.168.1.218:18080/resume/renderTemplateA01', // 测试环境
        generatePDFUrl: 'http://192.168.1.218:18080/resume/export-pdf',
        exportJpegUrl: 'http://192.168.1.218:18080/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: '/auth/login', // 用户登录
        userInfoUrl: 'http://192.168.1.218:18080/auth/user', // 获取用户信息
        updateUserInfoUrl: 'http://192.168.1.218:18080/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl: 'http://192.168.1.218:18080/feedback/submit', // 提交用户反馈

        // 模板相关API
        templatesUrl: 'http://192.168.1.218:18080/templates',        // 获取模板列表
        templateDetailUrl: 'http://192.168.1.218:18080/templates',        // 获取模板详情
        templateDownloadUrl: 'http://192.168.1.218:18080/templates',        // 记录模板下载
        templateCategoriesUrl: 'http://192.168.1.218:18080/templates/categories', // 获取模板分类
        templateSearchUrl: 'http://192.168.1.218:18080/templates/search', // 搜索模板
        popularTemplatesUrl: 'http://192.168.1.218:18080/templates/popular' // 获取热门模板
    },

    test_out: {
        // 基础URL
        baseUrl: 'http://127.0.0.1:18080',

        // 简历相关API
        renderTemplateUrl:        '/resume/renderTemplateA01', // 测试环境
        generatePDFUrl:           '/resume/export-pdf',
        exportJpegUrl:            '/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl:                 '/auth/login', // 用户登录
        userInfoUrl:              '/auth/user', // 获取用户信息
        updateUserInfoUrl:        '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl:              '/feedback/submit', // 提交用户反馈

        // 模板相关API
        templatesUrl:             '/templates',        // 获取模板列表
        templateDetailUrl:        '/templates',        // 获取模板详情
        templateDownloadUrl:      '/templates',        // 记录模板下载
        templateCategoriesUrl:    '/templates/categories', // 获取模板分类
        templateSearchUrl:        '/templates/search', // 搜索模板
        popularTemplatesUrl:      '/templates/popular' // 获取热门模板
    },


    prod: {
        // 基础URL
        baseUrl: 'https://gbw8848.cn',

        // 简历相关API
        renderTemplateUrl: 'https://gbw8848.cn/resume/renderTemplateA01', // 生产环境
        generatePDFUrl: 'https://gbw8848.cn/resume/export-pdf',
        exportJpegUrl: 'https://gbw8848.cn/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: '/auth/login', // 用户登录
        userInfoUrl: '/auth/user', // 获取用户信息
        updateUserInfoUrl: '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl: '/feedback/submit', // 提交用户反馈

        // 模板相关API
        templatesUrl: '/templates',        // 获取模板列表
        templateDetailUrl: '/templates',        // 获取模板详情
        templateDownloadUrl: '/templates',        // 记录模板下载
        templateCategoriesUrl: '/templates/categories', // 获取模板分类
        templateSearchUrl: '/templates/search', // 搜索模板
        popularTemplatesUrl: '/templates/popular', // 获取热门模板

        // 简历样式相关API
        resumeStylesUrl: '/free-templates/styles',    // 获取简历样式模板列表
        resumeStyleDetailUrl: '/free-templates/styles'    // 获取简历样式模板详情
    }
};

// 根据环境导出配置
// 优先级：开发环境 > 测试环境 > 生产环境
function getEnvironment() {
  // 检查是否在开发者工具中
  const accountInfo = wx.getAccountInfoSync();
  if (accountInfo.miniProgram.envVersion === 'develop') {
    return 'dev';
  } else if (accountInfo.miniProgram.envVersion === 'trial') {
    return 'test';
  } else {
    return 'prod';
  }
}

const env = getEnvironment();
console.log('当前API环境:', env, apiConfig[env]);

// 导出当前环境配置，同时包含全局超时配置
module.exports = {
  ...apiConfig[env],
  timeout: apiConfig.timeout
};